<?xml version="1.0"?>
<!--
TCP 互動功能啟動文件

功能：
- 啟動 TCP IK 求解服務
- 配置 MoveIt! 規劃環境
- 設置機器人參數
- 啟動必要的 ROS 節點

作者: ROS UI Control Team
版本: 1.0.0
-->

<launch>
    <!-- 參數配置 -->
    <arg name="robot_name" default="ur5" doc="機器人名稱"/>
    <arg name="planning_group" default="manipulator" doc="MoveIt! 規劃群組"/>
    <arg name="end_effector_link" default="tool0" doc="末端執行器連桿"/>
    <arg name="base_frame" default="base_link" doc="基座框架"/>
    <arg name="ik_timeout" default="5.0" doc="IK 求解超時時間（秒）"/>
    <arg name="ik_attempts" default="10" doc="IK 求解嘗試次數"/>
    <arg name="use_sim_time" default="false" doc="是否使用模擬時間"/>
    <arg name="debug" default="false" doc="是否啟用調試模式"/>

    <!-- 設置使用模擬時間 -->
    <param name="use_sim_time" value="$(arg use_sim_time)"/>

    <!-- 載入機器人描述 -->
    <param name="robot_description" 
           command="$(find xacro)/xacro --inorder '$(find ur_description)/urdf/$(arg robot_name).urdf.xacro'"/>

    <!-- 載入機器人語義描述 -->
    <param name="robot_description_semantic" 
           command="$(find xacro)/xacro --inorder '$(find ur5_moveit_config)/config/$(arg robot_name).srdf'"/>

    <!-- 載入運動學和動力學參數 -->
    <rosparam file="$(find ur5_moveit_config)/config/kinematics.yaml" command="load"/>
    <rosparam file="$(find ur5_moveit_config)/config/joint_limits.yaml" command="load"/>

    <!-- 載入規劃參數 -->
    <rosparam file="$(find ur5_moveit_config)/config/ompl_planning.yaml" command="load"/>

    <!-- 啟動 robot_state_publisher -->
    <node name="robot_state_publisher" 
          pkg="robot_state_publisher" 
          type="robot_state_publisher" 
          respawn="false" 
          output="screen">
        <remap from="/joint_states" to="/joint_states"/>
    </node>

    <!-- 啟動 MoveIt! move_group 節點 -->
    <include file="$(find ur5_moveit_config)/launch/move_group.launch">
        <arg name="allow_trajectory_execution" value="true"/>
        <arg name="fake_execution" value="false"/>
        <arg name="info" value="true"/>
        <arg name="debug" value="$(arg debug)"/>
    </include>

    <!-- 啟動 TCP IK 服務 -->
    <node name="tcp_ik_service" 
          pkg="ros_ui_control" 
          type="tcp_ik_service.py" 
          output="screen" 
          respawn="true">
        
        <!-- 服務參數 -->
        <param name="planning_group" value="$(arg planning_group)"/>
        <param name="end_effector_link" value="$(arg end_effector_link)"/>
        <param name="base_frame" value="$(arg base_frame)"/>
        <param name="ik_timeout" value="$(arg ik_timeout)"/>
        <param name="ik_attempts" value="$(arg ik_attempts)"/>
        
        <!-- 調試參數 -->
        <param name="debug" value="$(arg debug)"/>
        
        <!-- 重映射 -->
        <remap from="/joint_states" to="/joint_states"/>
        <remap from="/joint_command" to="/joint_command"/>
        <remap from="/tcp_target_pose" to="/tcp_target_pose"/>
    </node>

    <!-- 啟動關節狀態發布器（如果需要模擬） -->
    <group if="$(arg use_sim_time)">
        <node name="joint_state_publisher" 
              pkg="joint_state_publisher" 
              type="joint_state_publisher">
            <param name="use_gui" value="false"/>
            <rosparam param="source_list">["/joint_command"]</rosparam>
        </node>
    </group>

    <!-- 啟動 RViz（可選） -->
    <group if="$(arg debug)">
        <node name="rviz" 
              pkg="rviz" 
              type="rviz" 
              args="-d $(find ur5_moveit_config)/config/moveit.rviz" 
              output="screen">
            <remap from="/joint_states" to="/joint_states"/>
        </node>
    </group>

    <!-- 啟動 rosbridge_server（用於 Web 介面通訊） -->
    <include file="$(find rosbridge_server)/launch/rosbridge_websocket.launch">
        <arg name="port" value="9090"/>
    </include>

    <!-- 啟動 tf2_web_republisher（用於 Web TF 支援） -->
    <node name="tf2_web_republisher" 
          pkg="tf2_web_republisher" 
          type="tf2_web_republisher" 
          output="screen">
        <param name="angular_thres" value="0.01"/>
        <param name="trans_thres" value="0.01"/>
    </node>

    <!-- 日誌輸出 -->
    <node name="tcp_interaction_logger" 
          pkg="rospy_tutorials" 
          type="talker" 
          name="startup_logger" 
          output="screen">
        <param name="message" value="TCP 互動系統已啟動"/>
    </node>

</launch>
