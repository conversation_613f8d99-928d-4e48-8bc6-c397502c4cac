#!/usr/bin/env python3
"""
TCP IK Service - TCP 互動的 IK 求解服務

功能：
- 提供 MoveIt! IK 求解服務
- 處理 TCP 位置到關節角度的轉換
- 關節限制檢查和碰撞檢測
- 實時 IK 求解和結果發布

作者: ROS UI Control Team
版本: 1.0.0
"""

import rospy
import tf2_ros
import tf2_geometry_msgs
from geometry_msgs.msg import PoseStamped, Pose
from sensor_msgs.msg import JointState
from moveit_msgs.msg import GetPositionIK, PositionIKRequest
from moveit_msgs.srv import GetPositionIKRequest, GetPositionIKResponse
import moveit_commander
import moveit_msgs.msg
from std_msgs.msg import Header
import numpy as np
from threading import Lock
import time


class TCPIKService:
    """TCP IK 求解服務類"""
    
    def __init__(self):
        """初始化 IK 服務"""
        # ROS 節點初始化
        rospy.init_node('tcp_ik_service', anonymous=True)
        
        # 配置參數
        self.robot_description = rospy.get_param('~robot_description', 'robot_description')
        self.planning_group = rospy.get_param('~planning_group', 'manipulator')
        self.end_effector_link = rospy.get_param('~end_effector_link', 'tool0')
        self.base_frame = rospy.get_param('~base_frame', 'base_link')
        self.ik_timeout = rospy.get_param('~ik_timeout', 5.0)
        self.ik_attempts = rospy.get_param('~ik_attempts', 10)
        
        # MoveIt! 初始化
        self.robot = None
        self.scene = None
        self.move_group = None
        self.planning_frame = None
        self.eef_link = None
        
        # TF 監聽器
        self.tf_buffer = tf2_ros.Buffer()
        self.tf_listener = tf2_ros.TransformListener(self.tf_buffer)
        
        # 線程鎖
        self.lock = Lock()
        
        # 統計信息
        self.stats = {
            'total_requests': 0,
            'successful_solutions': 0,
            'failed_solutions': 0,
            'average_solve_time': 0.0
        }
        
        # 初始化 MoveIt!
        self.initialize_moveit()
        
        # 設置服務和訂閱者
        self.setup_ros_interfaces()
        
        rospy.loginfo("TCP IK Service 已啟動")

    def initialize_moveit(self):
        """初始化 MoveIt! 組件"""
        try:
            # 初始化 moveit_commander
            moveit_commander.roscpp_initialize([])
            
            # 創建 RobotCommander 對象
            self.robot = moveit_commander.RobotCommander()
            
            # 創建 PlanningSceneInterface 對象
            self.scene = moveit_commander.PlanningSceneInterface()
            
            # 創建 MoveGroupCommander 對象
            self.move_group = moveit_commander.MoveGroupCommander(self.planning_group)
            
            # 獲取規劃框架和末端執行器連桿
            self.planning_frame = self.move_group.get_planning_frame()
            self.eef_link = self.move_group.get_end_effector_link()
            
            # 設置 IK 求解參數
            self.move_group.set_num_planning_attempts(self.ik_attempts)
            self.move_group.set_planning_time(self.ik_timeout)
            
            rospy.loginfo(f"MoveIt! 已初始化:")
            rospy.loginfo(f"  規劃框架: {self.planning_frame}")
            rospy.loginfo(f"  末端執行器: {self.eef_link}")
            rospy.loginfo(f"  規劃群組: {self.planning_group}")
            
        except Exception as e:
            rospy.logerr(f"MoveIt! 初始化失敗: {e}")
            raise

    def setup_ros_interfaces(self):
        """設置 ROS 接口"""
        # IK 服務
        self.ik_service = rospy.Service(
            '/compute_ik', 
            GetPositionIK, 
            self.handle_ik_request
        )
        
        # TCP 目標位置訂閱者
        self.tcp_pose_sub = rospy.Subscriber(
            '/tcp_target_pose',
            PoseStamped,
            self.tcp_pose_callback,
            queue_size=1
        )
        
        # 關節指令發布者
        self.joint_command_pub = rospy.Publisher(
            '/joint_command',
            JointState,
            queue_size=1
        )
        
        # 當前關節狀態訂閱者
        self.joint_state_sub = rospy.Subscriber(
            '/joint_states',
            JointState,
            self.joint_state_callback,
            queue_size=1
        )
        
        # 當前關節狀態
        self.current_joint_state = None
        
        rospy.loginfo("ROS 接口已設置")

    def handle_ik_request(self, request):
        """處理 IK 求解請求"""
        start_time = time.time()
        
        with self.lock:
            self.stats['total_requests'] += 1
        
        try:
            # 提取目標位姿
            target_pose = request.ik_request.pose_stamped
            
            # 轉換到規劃框架
            if target_pose.header.frame_id != self.planning_frame:
                target_pose = self.transform_pose(target_pose, self.planning_frame)
            
            # 設置當前關節狀態
            if request.ik_request.robot_state.joint_state.name:
                current_joints = request.ik_request.robot_state.joint_state
                self.move_group.set_start_state_to_current_state()
                # 這裡可以設置特定的起始狀態
            
            # 設置目標位姿
            self.move_group.set_pose_target(target_pose.pose)
            
            # 計算 IK
            success = self.move_group.plan()
            
            if success[0]:  # success[0] 是布爾值，success[1] 是軌跡
                # 獲取關節值
                joint_values = success[1].joint_trajectory.points[-1].positions
                joint_names = success[1].joint_trajectory.joint_names
                
                # 創建回應
                response = GetPositionIKResponse()
                response.solution.joint_state.header.stamp = rospy.Time.now()
                response.solution.joint_state.name = joint_names
                response.solution.joint_state.position = joint_values
                response.error_code.val = moveit_msgs.msg.MoveItErrorCodes.SUCCESS
                
                # 更新統計
                solve_time = time.time() - start_time
                with self.lock:
                    self.stats['successful_solutions'] += 1
                    self.update_average_solve_time(solve_time)
                
                rospy.logdebug(f"IK 求解成功，耗時: {solve_time:.3f}s")
                return response
                
            else:
                # IK 求解失敗
                response = GetPositionIKResponse()
                response.error_code.val = moveit_msgs.msg.MoveItErrorCodes.NO_IK_SOLUTION
                
                with self.lock:
                    self.stats['failed_solutions'] += 1
                
                rospy.logwarn("IK 求解失敗：無解")
                return response
                
        except Exception as e:
            rospy.logerr(f"IK 求解過程中發生錯誤: {e}")
            
            response = GetPositionIKResponse()
            response.error_code.val = moveit_msgs.msg.MoveItErrorCodes.FAILURE
            
            with self.lock:
                self.stats['failed_solutions'] += 1
            
            return response

    def tcp_pose_callback(self, msg):
        """TCP 目標位置回調函數"""
        try:
            # 創建 IK 請求
            ik_request = GetPositionIKRequest()
            ik_request.ik_request.group_name = self.planning_group
            ik_request.ik_request.pose_stamped = msg
            ik_request.ik_request.timeout = rospy.Duration(self.ik_timeout)
            ik_request.ik_request.attempts = self.ik_attempts
            
            # 設置當前機器人狀態
            if self.current_joint_state:
                ik_request.ik_request.robot_state.joint_state = self.current_joint_state
            
            # 調用 IK 服務
            response = self.handle_ik_request(ik_request)
            
            # 如果求解成功，發布關節指令
            if response.error_code.val == moveit_msgs.msg.MoveItErrorCodes.SUCCESS:
                self.publish_joint_command(response.solution.joint_state)
                
        except Exception as e:
            rospy.logerr(f"TCP 位置回調處理失敗: {e}")

    def joint_state_callback(self, msg):
        """關節狀態回調函數"""
        self.current_joint_state = msg

    def publish_joint_command(self, joint_state):
        """發布關節指令"""
        try:
            # 創建關節指令消息
            command_msg = JointState()
            command_msg.header.stamp = rospy.Time.now()
            command_msg.name = joint_state.name
            command_msg.position = joint_state.position
            command_msg.velocity = [0.0] * len(joint_state.position)
            command_msg.effort = [0.0] * len(joint_state.position)
            
            # 發布指令
            self.joint_command_pub.publish(command_msg)
            
            rospy.logdebug("關節指令已發布")
            
        except Exception as e:
            rospy.logerr(f"發布關節指令失敗: {e}")

    def transform_pose(self, pose_stamped, target_frame):
        """轉換位姿到目標框架"""
        try:
            # 等待變換可用
            self.tf_buffer.can_transform(
                target_frame,
                pose_stamped.header.frame_id,
                pose_stamped.header.stamp,
                rospy.Duration(1.0)
            )
            
            # 執行變換
            transformed_pose = tf2_geometry_msgs.do_transform_pose(
                pose_stamped,
                self.tf_buffer.lookup_transform(
                    target_frame,
                    pose_stamped.header.frame_id,
                    pose_stamped.header.stamp
                )
            )
            
            return transformed_pose
            
        except Exception as e:
            rospy.logerr(f"位姿變換失敗: {e}")
            return pose_stamped

    def update_average_solve_time(self, new_time):
        """更新平均求解時間"""
        total_successful = self.stats['successful_solutions']
        if total_successful > 0:
            self.stats['average_solve_time'] = (
                (self.stats['average_solve_time'] * (total_successful - 1) + new_time) 
                / total_successful
            )

    def get_stats(self):
        """獲取統計信息"""
        with self.lock:
            return self.stats.copy()

    def print_stats(self):
        """打印統計信息"""
        stats = self.get_stats()
        success_rate = 0.0
        if stats['total_requests'] > 0:
            success_rate = stats['successful_solutions'] / stats['total_requests'] * 100
        
        rospy.loginfo("=== IK 服務統計 ===")
        rospy.loginfo(f"總請求數: {stats['total_requests']}")
        rospy.loginfo(f"成功求解: {stats['successful_solutions']}")
        rospy.loginfo(f"失敗求解: {stats['failed_solutions']}")
        rospy.loginfo(f"成功率: {success_rate:.1f}%")
        rospy.loginfo(f"平均求解時間: {stats['average_solve_time']:.3f}s")

    def run(self):
        """運行服務"""
        rospy.loginfo("TCP IK Service 正在運行...")
        
        # 定期打印統計信息
        rate = rospy.Rate(0.1)  # 10秒一次
        while not rospy.is_shutdown():
            try:
                rate.sleep()
                if self.stats['total_requests'] > 0:
                    self.print_stats()
            except rospy.ROSInterruptException:
                break
        
        rospy.loginfo("TCP IK Service 已停止")


def main():
    """主函數"""
    try:
        # 創建並運行服務
        ik_service = TCPIKService()
        ik_service.run()
        
    except rospy.ROSInterruptException:
        rospy.loginfo("程序被中斷")
    except Exception as e:
        rospy.logerr(f"程序運行失敗: {e}")
    finally:
        # 清理 MoveIt!
        moveit_commander.roscpp_shutdown()


if __name__ == '__main__':
    main()
