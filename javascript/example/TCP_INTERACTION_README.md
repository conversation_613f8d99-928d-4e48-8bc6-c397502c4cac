# TCP 互動功能實現指南

## 📋 功能概述

本功能實現了通過拖拽機器人 TCP (Tool Center Point) 來自動計算逆運動學 (IK) 的互動系統。

### 🎯 主要功能
- **TCP 視覺化**: 在 3D 場景中顯示可拖拽的 TCP 指示器
- **互動拖拽**: 滑鼠拖拽 TCP 位置，實時更新目標位置
- **自動 IK 計算**: 根據 TCP 位置自動調用 MoveIt! IK 服務
- **關節更新**: IK 求解成功後自動更新機器人關節角度
- **狀態監控**: 實時顯示 IK 求解統計和 TCP 位置信息

## 🏗️ 系統架構

### 前端架構 (JavaScript)
```
TCP 互動系統
├── TCPDragController      # TCP 拖拽控制器
│   ├── 滑鼠事件處理
│   ├── 3D 位置計算
│   ├── 拖拽約束檢查
│   └── ROS 消息發布
├── IKSolverManager        # IK 求解管理器
│   ├── MoveIt! 服務調用
│   ├── 求解結果驗證
│   ├── 統計信息管理
│   └── 錯誤處理
├── TCPInteractionUI       # TCP 互動 UI
│   ├── 控制面板
│   ├── 狀態顯示
│   ├── 位置監控
│   └── 統計信息
└── AppManager 整合        # 主系統整合
    ├── 功能初始化
    ├── 模式切換
    └── 資源管理
```

### 後端架構 (ROS)
```
ROS 後端系統
├── tcp_ik_service.py      # IK 求解服務
│   ├── MoveIt! 整合
│   ├── IK 計算
│   ├── 關節限制檢查
│   └── 結果發布
├── MoveIt! 規劃環境       # 運動規劃
│   ├── 運動學模型
│   ├── 碰撞檢測
│   └── 路徑規劃
└── ROS 通訊               # 消息傳遞
    ├── rosbridge_server
    ├── tf2_web_republisher
    └── 主題發布/訂閱
```

## 🚀 安裝和配置

### 1. 前端依賴
確保已安裝以下 JavaScript 模組：
```javascript
// 已包含在現有項目中
import { TCPDragController } from './managers/tcp_drag_controller.js';
import { IKSolverManager } from './managers/ik_solver_manager.js';
import { TCPInteractionUI } from './components/tcp_interaction_ui.js';
```

### 2. ROS 後端依賴
```bash
# 安裝 MoveIt!
sudo apt-get install ros-noetic-moveit

# 安裝 rosbridge
sudo apt-get install ros-noetic-rosbridge-server

# 安裝 tf2_web_republisher
sudo apt-get install ros-noetic-tf2-web-republisher

# 安裝機器人描述包（以 UR5 為例）
sudo apt-get install ros-noetic-ur-description
sudo apt-get install ros-noetic-ur5-moveit-config
```

### 3. 配置機器人參數
編輯 `tcp_interaction.launch` 文件中的參數：
```xml
<arg name="robot_name" default="ur5"/>
<arg name="planning_group" default="manipulator"/>
<arg name="end_effector_link" default="tool0"/>
<arg name="base_frame" default="base_link"/>
```

## 🎮 使用方法

### 1. 啟動 ROS 後端
```bash
# 啟動 TCP 互動系統
roslaunch ros_ui_control tcp_interaction.launch

# 或者啟動調試模式（包含 RViz）
roslaunch ros_ui_control tcp_interaction.launch debug:=true
```

### 2. 啟動前端
```bash
# 進入項目目錄
cd javascript/example

# 啟動 HTTP 服務器
python3 -m http.server 8081

# 打開瀏覽器
open http://localhost:8081/rozviz.html
```

### 3. 啟用 TCP 互動
1. 在網頁界面中找到 "TCP 互動控制" 面板
2. 點擊 "啟用 TCP 互動" 按鈕
3. 在 3D 場景中會出現紅色的 TCP 指示器球體
4. 拖拽紅色球體來移動 TCP 位置
5. 系統會自動計算 IK 並更新機器人姿態

## 🔧 配置選項

### TCP 拖拽控制器配置
```javascript
const tcpController = new TCPDragController(viewer, {
    tcpLinkName: 'tool0',           // TCP 連桿名稱
    enableIK: true,                 // 是否啟用 IK
    ikServiceName: '/compute_ik',   // IK 服務名稱
    tcpPoseTopic: '/tcp_target_pose', // TCP 目標位置 Topic
    dragSensitivity: 1.0,           // 拖拽靈敏度
    positionConstraints: {          // 位置約束
        minX: -2.0, maxX: 2.0,
        minY: -2.0, maxY: 2.0,
        minZ: 0.0,  maxZ: 2.0
    },
    visualFeedback: true            // 是否顯示視覺反饋
});
```

### IK 求解器配置
```javascript
const ikSolver = new IKSolverManager({
    ikServiceName: '/compute_ik',
    planningGroup: 'manipulator',
    endEffectorLink: 'tool0',
    baseFrame: 'base_link',
    timeout: 5.0,
    attempts: 10,
    validateSolution: true
});
```

## 📊 監控和調試

### 1. 前端監控
- **TCP 位置顯示**: 實時顯示 TCP 的 X, Y, Z 座標
- **IK 統計**: 顯示成功率、平均求解時間、總請求數
- **狀態指示器**: 顯示 TCP 互動的啟用/禁用狀態

### 2. ROS 後端監控
```bash
# 查看 IK 服務狀態
rosservice info /compute_ik

# 監控 TCP 目標位置
rostopic echo /tcp_target_pose

# 監控關節指令
rostopic echo /joint_command

# 查看 TF 樹
rosrun tf2_tools view_frames.py
```

### 3. 調試工具
```bash
# 啟動 RViz 進行視覺化調試
roslaunch ros_ui_control tcp_interaction.launch debug:=true

# 查看 MoveIt! 規劃場景
rosrun moveit_ros_planning_interface moveit_cpp_tutorial

# 測試 IK 服務
rosservice call /compute_ik "ik_request: {...}"
```

## ⚠️ 常見問題

### 1. IK 求解失敗
**問題**: TCP 拖拽時 IK 求解經常失敗
**解決方案**:
- 檢查目標位置是否在機器人工作空間內
- 調整 IK 求解參數（嘗試次數、超時時間）
- 確認機器人模型和 MoveIt! 配置正確

### 2. TCP 指示器不顯示
**問題**: 3D 場景中看不到紅色的 TCP 指示器
**解決方案**:
- 確認 TCP 連桿名稱配置正確
- 檢查 URDF 模型是否包含指定的連桿
- 確認 TCP 互動功能已正確啟用

### 3. 拖拽沒有反應
**問題**: 拖拽 TCP 指示器時機器人不動
**解決方案**:
- 檢查 ROS 連接狀態
- 確認 IK 服務正在運行
- 查看瀏覽器控制台的錯誤信息

### 4. 關節運動不平滑
**問題**: 機器人關節運動不連續或跳躍
**解決方案**:
- 調整拖拽靈敏度參數
- 增加 IK 求解的嘗試次數
- 實現軌跡平滑算法

## 🔮 擴展功能

### 1. 方向控制
可以擴展為同時控制 TCP 的位置和方向：
```javascript
// 添加方向控制
const orientationController = new TCPOrientationController(viewer);
```

### 2. 約束控制
實現更複雜的運動約束：
```javascript
// 添加運動約束
const constraintManager = new MotionConstraintManager({
    positionConstraints: [...],
    orientationConstraints: [...],
    jointConstraints: [...]
});
```

### 3. 軌跡規劃
整合完整的軌跡規劃功能：
```javascript
// 添加軌跡規劃
const trajectoryPlanner = new TrajectoryPlanner(moveGroup);
```

## 📝 API 參考

### TCPDragController
- `enable()`: 啟用 TCP 拖拽
- `disable()`: 禁用 TCP 拖拽
- `updateTCPPosition()`: 更新 TCP 位置
- `publishTCPPose(position, orientation)`: 發布 TCP 位姿

### IKSolverManager
- `solveIK(targetPose, currentJointState)`: 求解 IK
- `getStats()`: 獲取統計信息
- `resetStats()`: 重置統計信息

### TCPInteractionUI
- `enableTCPInteraction()`: 啟用 TCP 互動
- `disableTCPInteraction()`: 禁用 TCP 互動
- `updateUI()`: 更新 UI 狀態

## 📄 授權

本項目採用 MIT 授權條款。詳見 LICENSE 文件。

## 👥 貢獻

歡迎提交 Issue 和 Pull Request 來改進這個項目。

## 📞 支援

如有問題，請聯繫 ROS UI Control Team。
