/**
 * TCPInteractionUI - TCP 互動使用者介面
 * 
 * 功能：
 * - TCP 互動開關控制
 * - IK 求解狀態顯示
 * - TCP 位置資訊顯示
 * - 互動模式切換
 * 
 * @module TCPInteractionUI
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

// ==================== 導入區塊 ====================
import { ELEMENT_IDS } from '../shared/config.js';

// ==================== 類別定義 ====================

/**
 * TCPInteractionUI 類別 - TCP 互動使用者介面管理器
 * 
 * @class TCPInteractionUI
 * @description 管理 TCP 互動功能的使用者介面
 */
export class TCPInteractionUI {
    
    // ==================== 建構函數 ====================
    
    /**
     * 建構函數
     * 
     * @param {Object} appManager - 應用程式管理器
     * @param {Object} options - 配置選項
     */
    constructor(appManager, options = {}) {
        // 核心依賴
        this.appManager = appManager;
        
        // 配置參數
        this.config = {
            containerId: 'tcp-interaction-panel',
            autoUpdate: true,
            updateInterval: 100, // ms
            showPosition: true,
            showIKStats: true,
            ...options
        };

        // UI 狀態
        this.isEnabled = false;
        this.updateTimer = null;
        this.lastTCPPosition = { x: 0, y: 0, z: 0 };

        // DOM 元素緩存
        this.elements = {
            container: null,
            toggleButton: null,
            statusIndicator: null,
            positionDisplay: null,
            ikStatsDisplay: null
        };

        this.initialize();
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化 TCP 互動 UI
     */
    initialize() {
        this.createUI();
        this.bindEvents();
        this.startAutoUpdate();

        console.log('[TCPInteractionUI] TCP 互動 UI 已初始化');
    }

    /**
     * 創建 UI 元素
     */
    createUI() {
        // 尋找或創建容器
        this.elements.container = document.getElementById(this.config.containerId);
        if (!this.elements.container) {
            this.elements.container = this.createContainer();
        }

        // 創建 UI 內容
        this.elements.container.innerHTML = this.generateUIHTML();

        // 緩存 DOM 元素
        this.cacheElements();

        console.log('[TCPInteractionUI] UI 元素已創建');
    }

    /**
     * 創建容器元素
     * 
     * @returns {HTMLElement} 容器元素
     */
    createContainer() {
        const container = document.createElement('div');
        container.id = this.config.containerId;
        container.className = 'tcp-interaction-panel';

        // 添加到主控制面板
        const viewControls = document.getElementById(ELEMENT_IDS.VIEW_CONTROLS);
        if (viewControls) {
            viewControls.appendChild(container);
        } else {
            document.body.appendChild(container);
        }

        return container;
    }

    /**
     * 生成 UI HTML
     * 
     * @returns {string} HTML 字符串
     */
    generateUIHTML() {
        return `
            <div class="tcp-interaction-header">
                <h3>TCP 互動控制</h3>
                <div class="tcp-status-indicator" id="tcp-status-indicator">
                    <span class="status-dot"></span>
                    <span class="status-text">已禁用</span>
                </div>
            </div>

            <div class="tcp-interaction-controls">
                <button id="tcp-toggle-btn" class="tcp-toggle-btn">
                    <span class="btn-icon">🎯</span>
                    <span class="btn-text">啟用 TCP 互動</span>
                </button>

                <div class="tcp-mode-selector">
                    <label>互動模式：</label>
                    <select id="tcp-mode-select">
                        <option value="position">位置控制</option>
                        <option value="pose">位姿控制</option>
                        <option value="constrained">約束控制</option>
                    </select>
                </div>
            </div>

            ${this.config.showPosition ? this.generatePositionDisplayHTML() : ''}
            ${this.config.showIKStats ? this.generateIKStatsHTML() : ''}

            <div class="tcp-interaction-info">
                <p class="info-text">
                    <span class="info-icon">💡</span>
                    拖拽紅色球體來移動 TCP 位置，系統會自動計算逆運動學
                </p>
            </div>
        `;
    }

    /**
     * 生成位置顯示 HTML
     * 
     * @returns {string} HTML 字符串
     */
    generatePositionDisplayHTML() {
        return `
            <div class="tcp-position-display" id="tcp-position-display">
                <h4>TCP 位置</h4>
                <div class="position-values">
                    <div class="position-item">
                        <label>X:</label>
                        <span id="tcp-pos-x">0.000</span> m
                    </div>
                    <div class="position-item">
                        <label>Y:</label>
                        <span id="tcp-pos-y">0.000</span> m
                    </div>
                    <div class="position-item">
                        <label>Z:</label>
                        <span id="tcp-pos-z">0.000</span> m
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成 IK 統計 HTML
     * 
     * @returns {string} HTML 字符串
     */
    generateIKStatsHTML() {
        return `
            <div class="tcp-ik-stats" id="tcp-ik-stats">
                <h4>IK 求解統計</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <label>成功率:</label>
                        <span id="ik-success-rate">0%</span>
                    </div>
                    <div class="stat-item">
                        <label>平均時間:</label>
                        <span id="ik-avg-time">0ms</span>
                    </div>
                    <div class="stat-item">
                        <label>總請求:</label>
                        <span id="ik-total-requests">0</span>
                    </div>
                    <div class="stat-item">
                        <label>最後狀態:</label>
                        <span id="ik-last-status">待機</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 緩存 DOM 元素
     */
    cacheElements() {
        this.elements.toggleButton = document.getElementById('tcp-toggle-btn');
        this.elements.statusIndicator = document.getElementById('tcp-status-indicator');
        this.elements.positionDisplay = document.getElementById('tcp-position-display');
        this.elements.ikStatsDisplay = document.getElementById('tcp-ik-stats');
        this.elements.modeSelect = document.getElementById('tcp-mode-select');

        // 位置顯示元素
        this.elements.posX = document.getElementById('tcp-pos-x');
        this.elements.posY = document.getElementById('tcp-pos-y');
        this.elements.posZ = document.getElementById('tcp-pos-z');

        // IK 統計元素
        this.elements.ikSuccessRate = document.getElementById('ik-success-rate');
        this.elements.ikAvgTime = document.getElementById('ik-avg-time');
        this.elements.ikTotalRequests = document.getElementById('ik-total-requests');
        this.elements.ikLastStatus = document.getElementById('ik-last-status');
    }

    // ==================== 事件綁定方法 ====================

    /**
     * 綁定事件
     */
    bindEvents() {
        // TCP 開關按鈕
        if (this.elements.toggleButton) {
            this.elements.toggleButton.addEventListener('click', () => {
                this.toggleTCPInteraction();
            });
        }

        // 模式選擇
        if (this.elements.modeSelect) {
            this.elements.modeSelect.addEventListener('change', (e) => {
                this.changeTCPMode(e.target.value);
            });
        }

        console.log('[TCPInteractionUI] 事件已綁定');
    }

    // ==================== 控制方法 ====================

    /**
     * 切換 TCP 互動狀態
     */
    toggleTCPInteraction() {
        if (this.isEnabled) {
            this.disableTCPInteraction();
        } else {
            this.enableTCPInteraction();
        }
    }

    /**
     * 啟用 TCP 互動
     */
    enableTCPInteraction() {
        this.appManager.enableTCPInteraction();
        this.isEnabled = true;
        this.updateUI();
        console.log('[TCPInteractionUI] TCP 互動已啟用');
    }

    /**
     * 禁用 TCP 互動
     */
    disableTCPInteraction() {
        this.appManager.disableTCPInteraction();
        this.isEnabled = false;
        this.updateUI();
        console.log('[TCPInteractionUI] TCP 互動已禁用');
    }

    /**
     * 改變 TCP 模式
     * 
     * @param {string} mode - 新模式
     */
    changeTCPMode(mode) {
        const tcpController = this.appManager.getTCPDragController();
        if (tcpController) {
            // 這裡可以實現不同的互動模式
            console.log(`[TCPInteractionUI] TCP 模式已切換到: ${mode}`);
        }
    }

    // ==================== UI 更新方法 ====================

    /**
     * 更新 UI 狀態
     */
    updateUI() {
        this.updateStatusIndicator();
        this.updateToggleButton();
        this.updatePositionDisplay();
        this.updateIKStats();
    }

    /**
     * 更新狀態指示器
     */
    updateStatusIndicator() {
        if (!this.elements.statusIndicator) return;

        const statusDot = this.elements.statusIndicator.querySelector('.status-dot');
        const statusText = this.elements.statusIndicator.querySelector('.status-text');

        if (this.isEnabled) {
            statusDot.className = 'status-dot active';
            statusText.textContent = '已啟用';
        } else {
            statusDot.className = 'status-dot';
            statusText.textContent = '已禁用';
        }
    }

    /**
     * 更新切換按鈕
     */
    updateToggleButton() {
        if (!this.elements.toggleButton) return;

        const btnText = this.elements.toggleButton.querySelector('.btn-text');
        if (this.isEnabled) {
            this.elements.toggleButton.className = 'tcp-toggle-btn active';
            btnText.textContent = '禁用 TCP 互動';
        } else {
            this.elements.toggleButton.className = 'tcp-toggle-btn';
            btnText.textContent = '啟用 TCP 互動';
        }
    }

    /**
     * 更新位置顯示
     */
    updatePositionDisplay() {
        if (!this.config.showPosition || !this.elements.posX) return;

        const tcpController = this.appManager.getTCPDragController();
        if (tcpController && tcpController.tcpWorldPosition) {
            const pos = tcpController.tcpWorldPosition;
            this.elements.posX.textContent = pos.x.toFixed(3);
            this.elements.posY.textContent = pos.y.toFixed(3);
            this.elements.posZ.textContent = pos.z.toFixed(3);
        }
    }

    /**
     * 更新 IK 統計
     */
    updateIKStats() {
        if (!this.config.showIKStats || !this.elements.ikSuccessRate) return;

        const ikManager = this.appManager.getIKSolverManager();
        if (ikManager) {
            const stats = ikManager.getStats();
            const successRate = stats.totalRequests > 0 ? 
                (stats.successfulSolutions / stats.totalRequests * 100).toFixed(1) : 0;

            this.elements.ikSuccessRate.textContent = `${successRate}%`;
            this.elements.ikAvgTime.textContent = `${Math.round(stats.averageSolveTime)}ms`;
            this.elements.ikTotalRequests.textContent = stats.totalRequests;
        }
    }

    // ==================== 自動更新方法 ====================

    /**
     * 開始自動更新
     */
    startAutoUpdate() {
        if (!this.config.autoUpdate) return;

        this.updateTimer = setInterval(() => {
            if (this.isEnabled) {
                this.updatePositionDisplay();
                this.updateIKStats();
            }
        }, this.config.updateInterval);
    }

    /**
     * 停止自動更新
     */
    stopAutoUpdate() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            this.updateTimer = null;
        }
    }

    // ==================== 清理方法 ====================

    /**
     * 清理資源
     */
    dispose() {
        this.stopAutoUpdate();
        
        if (this.elements.container) {
            this.elements.container.remove();
        }

        console.log('[TCPInteractionUI] 資源已清理');
    }
}
