/**
 * TCPDragController - TCP 拖拽控制器
 * 
 * 功能：
 * - TCP 位置的互動拖拽
 * - 3D 空間中的位置計算
 * - 與 IK 求解器的整合
 * - 視覺反饋和約束處理
 * 
 * @module TCPDragController
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

// ==================== 導入區塊 ====================
import * as THREE from 'three';
import { rosPublisherManager } from '../ros/ros_publisher_manager.js';

// ==================== 類別定義 ====================

/**
 * TCPDragController 類別 - TCP 拖拽控制管理器
 * 
 * @class TCPDragController
 * @description 管理機器人 TCP 的互動拖拽和 IK 計算
 */
export class TCPDragController {
    
    // ==================== 建構函數 ====================
    
    /**
     * 建構函數
     * 
     * @param {Object} viewer - URDF 檢視器實例
     * @param {Object} options - 配置選項
     */
    constructor(viewer, options = {}) {
        // 核心依賴
        this.viewer = viewer;
        this.scene = viewer.scene;
        this.camera = viewer.camera;
        this.renderer = viewer.renderer;

        // 配置參數
        this.config = {
            tcpLinkName: 'tool0',           // TCP 連桿名稱
            enableIK: true,                 // 是否啟用 IK
            ikServiceName: '/compute_ik',   // IK 服務名稱
            tcpPoseTopic: '/tcp_target_pose', // TCP 目標位置 Topic
            jointCommandTopic: '/joint_command', // 關節指令 Topic
            dragSensitivity: 1.0,           // 拖拽靈敏度
            positionConstraints: {          // 位置約束
                minX: -2.0, maxX: 2.0,
                minY: -2.0, maxY: 2.0,
                minZ: 0.0,  maxZ: 2.0
            },
            visualFeedback: true,           // 是否顯示視覺反饋
            ...options
        };

        // 拖拽狀態
        this.isDragging = false;
        this.dragStartPosition = new THREE.Vector3();
        this.dragCurrentPosition = new THREE.Vector3();
        this.tcpWorldPosition = new THREE.Vector3();

        // Three.js 組件
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.dragPlane = new THREE.Plane();

        // TCP 相關
        this.tcpLink = null;
        this.tcpVisualizer = null;

        // 事件綁定
        this.onMouseDown = this.onMouseDown.bind(this);
        this.onMouseMove = this.onMouseMove.bind(this);
        this.onMouseUp = this.onMouseUp.bind(this);

        this.initialize();
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化 TCP 拖拽控制器
     */
    initialize() {
        this.findTCPLink();
        this.createTCPVisualizer();
        this.bindEvents();
        this.setupROSPublishers();

        console.log('[TCPDragController] TCP 拖拽控制器已初始化');
    }

    /**
     * 尋找 TCP 連桿
     */
    findTCPLink() {
        if (!this.viewer.robot) {
            console.warn('[TCPDragController] 機器人模型尚未載入');
            return;
        }

        // 遞歸搜尋 TCP 連桿
        this.tcpLink = this.findLinkByName(this.viewer.robot, this.config.tcpLinkName);
        
        if (this.tcpLink) {
            console.log(`[TCPDragController] 找到 TCP 連桿: ${this.config.tcpLinkName}`);
            this.updateTCPPosition();
        } else {
            console.warn(`[TCPDragController] 未找到 TCP 連桿: ${this.config.tcpLinkName}`);
        }
    }

    /**
     * 遞歸搜尋連桿
     * 
     * @param {Object} object - 3D 物件
     * @param {string} name - 連桿名稱
     * @returns {Object|null} 找到的連桿
     */
    findLinkByName(object, name) {
        if (object.name === name || (object.userData && object.userData.name === name)) {
            return object;
        }

        for (const child of object.children) {
            const result = this.findLinkByName(child, name);
            if (result) return result;
        }

        return null;
    }

    /**
     * 創建 TCP 視覺化組件
     */
    createTCPVisualizer() {
        if (!this.config.visualFeedback) return;

        // 創建 TCP 指示器（球體）
        const geometry = new THREE.SphereGeometry(0.05, 16, 16);
        const material = new THREE.MeshBasicMaterial({ 
            color: 0xff4444, 
            transparent: true, 
            opacity: 0.8 
        });
        
        this.tcpVisualizer = new THREE.Mesh(geometry, material);
        this.tcpVisualizer.name = 'tcp_visualizer';
        this.scene.add(this.tcpVisualizer);

        // 創建座標軸指示器
        const axesHelper = new THREE.AxesHelper(0.1);
        this.tcpVisualizer.add(axesHelper);

        console.log('[TCPDragController] TCP 視覺化組件已創建');
    }

    /**
     * 綁定滑鼠事件
     */
    bindEvents() {
        const canvas = this.renderer.domElement;
        
        canvas.addEventListener('mousedown', this.onMouseDown);
        canvas.addEventListener('mousemove', this.onMouseMove);
        canvas.addEventListener('mouseup', this.onMouseUp);
        
        console.log('[TCPDragController] 滑鼠事件已綁定');
    }

    /**
     * 設置 ROS 發布器
     */
    setupROSPublishers() {
        // 廣告 TCP 目標位置 Topic
        rosPublisherManager.advertise(
            this.config.tcpPoseTopic,
            'geometry_msgs/PoseStamped',
            { queueSize: 1 }
        );

        // 廣告關節指令 Topic
        rosPublisherManager.advertise(
            this.config.jointCommandTopic,
            'sensor_msgs/JointState',
            { queueSize: 1 }
        );

        console.log('[TCPDragController] ROS 發布器已設置');
    }

    // ==================== 事件處理方法 ====================

    /**
     * 滑鼠按下事件處理
     * 
     * @param {MouseEvent} event - 滑鼠事件
     */
    onMouseDown(event) {
        if (!this.tcpLink || !this.tcpVisualizer) return;

        // 更新滑鼠座標
        this.updateMousePosition(event);

        // 射線檢測
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObject(this.tcpVisualizer, true);

        if (intersects.length > 0) {
            this.startDragging(intersects[0].point);
        }
    }

    /**
     * 滑鼠移動事件處理
     * 
     * @param {MouseEvent} event - 滑鼠事件
     */
    onMouseMove(event) {
        if (!this.isDragging) return;

        // 更新滑鼠座標
        this.updateMousePosition(event);

        // 計算新的 TCP 位置
        this.updateDragPosition();
    }

    /**
     * 滑鼠放開事件處理
     * 
     * @param {MouseEvent} event - 滑鼠事件
     */
    onMouseUp(event) {
        if (this.isDragging) {
            this.stopDragging();
        }
    }

    // ==================== 拖拽控制方法 ====================

    /**
     * 開始拖拽
     * 
     * @param {THREE.Vector3} startPoint - 起始點
     */
    startDragging(startPoint) {
        this.isDragging = true;
        this.dragStartPosition.copy(startPoint);
        this.updateTCPPosition();

        // 設置拖拽平面（垂直於相機視線）
        const cameraDirection = new THREE.Vector3();
        this.camera.getWorldDirection(cameraDirection);
        this.dragPlane.setFromNormalAndCoplanarPoint(cameraDirection, startPoint);

        // 禁用軌道控制
        if (this.viewer.controls) {
            this.viewer.controls.enabled = false;
        }

        console.log('[TCPDragController] 開始拖拽 TCP');
    }

    /**
     * 更新拖拽位置
     */
    updateDragPosition() {
        // 射線與拖拽平面的交點
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersectPoint = new THREE.Vector3();
        
        if (this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint)) {
            // 應用位置約束
            this.applyPositionConstraints(intersectPoint);
            
            // 更新 TCP 視覺化位置
            if (this.tcpVisualizer) {
                this.tcpVisualizer.position.copy(intersectPoint);
            }

            // 發布 TCP 目標位置
            this.publishTCPPose(intersectPoint);

            // 如果啟用 IK，請求 IK 計算
            if (this.config.enableIK) {
                this.requestIKSolution(intersectPoint);
            }
        }
    }

    /**
     * 停止拖拽
     */
    stopDragging() {
        this.isDragging = false;

        // 重新啟用軌道控制
        if (this.viewer.controls) {
            this.viewer.controls.enabled = true;
        }

        console.log('[TCPDragController] 停止拖拽 TCP');
    }

    // ==================== 工具方法 ====================

    /**
     * 更新滑鼠位置
     * 
     * @param {MouseEvent} event - 滑鼠事件
     */
    updateMousePosition(event) {
        const rect = this.renderer.domElement.getBoundingClientRect();
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    /**
     * 更新 TCP 位置
     */
    updateTCPPosition() {
        if (!this.tcpLink) return;

        // 獲取 TCP 世界座標
        this.tcpLink.getWorldPosition(this.tcpWorldPosition);
        
        // 更新視覺化位置
        if (this.tcpVisualizer) {
            this.tcpVisualizer.position.copy(this.tcpWorldPosition);
        }
    }

    /**
     * 應用位置約束
     *
     * @param {THREE.Vector3} position - 位置向量
     */
    applyPositionConstraints(position) {
        const constraints = this.config.positionConstraints;

        position.x = Math.max(constraints.minX, Math.min(constraints.maxX, position.x));
        position.y = Math.max(constraints.minY, Math.min(constraints.maxY, position.y));
        position.z = Math.max(constraints.minZ, Math.min(constraints.maxZ, position.z));
    }

    // ==================== ROS 通訊方法 ====================

    /**
     * 發布 TCP 目標位置
     *
     * @param {THREE.Vector3} position - TCP 位置
     * @param {THREE.Quaternion} orientation - TCP 方向（可選）
     */
    publishTCPPose(position, orientation = null) {
        // 預設方向（如果未提供）
        if (!orientation) {
            orientation = new THREE.Quaternion(0, 0, 0, 1);
        }

        const poseMessage = {
            header: {
                stamp: { sec: Math.floor(Date.now() / 1000), nsec: 0 },
                frame_id: 'base_link'
            },
            pose: {
                position: {
                    x: position.x,
                    y: position.y,
                    z: position.z
                },
                orientation: {
                    x: orientation.x,
                    y: orientation.y,
                    z: orientation.z,
                    w: orientation.w
                }
            }
        };

        rosPublisherManager.publish(
            this.config.tcpPoseTopic,
            'geometry_msgs/PoseStamped',
            poseMessage
        );
    }

    /**
     * 請求 IK 求解
     *
     * @param {THREE.Vector3} targetPosition - 目標位置
     * @param {THREE.Quaternion} targetOrientation - 目標方向（可選）
     */
    async requestIKSolution(targetPosition, targetOrientation = null) {
        try {
            // 預設方向
            if (!targetOrientation) {
                targetOrientation = new THREE.Quaternion(0, 0, 0, 1);
            }

            // 構建 IK 請求
            const ikRequest = {
                ik_request: {
                    group_name: 'manipulator', // 機器人群組名稱
                    robot_state: {
                        joint_state: this.getCurrentJointState()
                    },
                    pose_stamped: {
                        header: {
                            stamp: { sec: Math.floor(Date.now() / 1000), nsec: 0 },
                            frame_id: 'base_link'
                        },
                        pose: {
                            position: {
                                x: targetPosition.x,
                                y: targetPosition.y,
                                z: targetPosition.z
                            },
                            orientation: {
                                x: targetOrientation.x,
                                y: targetOrientation.y,
                                z: targetOrientation.z,
                                w: targetOrientation.w
                            }
                        }
                    },
                    timeout: { sec: 5, nsec: 0 }
                }
            };

            // 調用 IK 服務
            this.callIKService(ikRequest);

        } catch (error) {
            console.error('[TCPDragController] IK 請求失敗:', error);
        }
    }

    /**
     * 調用 IK 服務
     *
     * @param {Object} request - IK 請求
     */
    callIKService(request) {
        // 這裡需要實現 ROS 服務調用
        // 由於 roslib.js 的服務調用，我們需要創建服務客戶端

        const ros = rosConnectionManager.getROS();
        if (!ros) {
            console.error('[TCPDragController] ROS 未連接');
            return;
        }

        const ikService = new window.ROSLIB.Service({
            ros: ros,
            name: this.config.ikServiceName,
            serviceType: 'moveit_msgs/GetPositionIK'
        });

        ikService.callService(request, (result) => {
            if (result.error_code.val === 1) { // SUCCESS
                console.log('[TCPDragController] IK 求解成功');
                this.applyIKSolution(result.solution.joint_state);
            } else {
                console.warn('[TCPDragController] IK 求解失敗，錯誤代碼:', result.error_code.val);
            }
        }, (error) => {
            console.error('[TCPDragController] IK 服務調用失敗:', error);
        });
    }

    /**
     * 應用 IK 求解結果
     *
     * @param {Object} jointState - 關節狀態
     */
    applyIKSolution(jointState) {
        // 更新機器人關節角度
        if (this.viewer.robot && jointState.name && jointState.position) {
            for (let i = 0; i < jointState.name.length; i++) {
                const jointName = jointState.name[i];
                const position = jointState.position[i];

                // 更新 URDF 檢視器中的關節
                this.viewer.setJointValue(jointName, position);
            }

            // 發布關節指令到 ROS
            this.publishJointCommand(jointState);
        }
    }

    /**
     * 發布關節指令
     *
     * @param {Object} jointState - 關節狀態
     */
    publishJointCommand(jointState) {
        const commandMessage = {
            header: {
                stamp: { sec: Math.floor(Date.now() / 1000), nsec: 0 }
            },
            name: jointState.name,
            position: jointState.position,
            velocity: new Array(jointState.name.length).fill(0),
            effort: new Array(jointState.name.length).fill(0)
        };

        rosPublisherManager.publish(
            this.config.jointCommandTopic,
            'sensor_msgs/JointState',
            commandMessage
        );
    }

    /**
     * 獲取當前關節狀態
     *
     * @returns {Object} 當前關節狀態
     */
    getCurrentJointState() {
        const jointNames = [];
        const jointPositions = [];

        if (this.viewer.robot && this.viewer.robot.joints) {
            Object.keys(this.viewer.robot.joints).forEach(jointName => {
                const joint = this.viewer.robot.joints[jointName];
                if (joint.jointType !== 'fixed') {
                    jointNames.push(jointName);
                    jointPositions.push(joint.angle || 0);
                }
            });
        }

        return {
            name: jointNames,
            position: jointPositions
        };
    }

    // ==================== 公共接口方法 ====================

    /**
     * 啟用 TCP 拖拽
     */
    enable() {
        this.config.enabled = true;
        if (this.tcpVisualizer) {
            this.tcpVisualizer.visible = true;
        }
        console.log('[TCPDragController] TCP 拖拽已啟用');
    }

    /**
     * 禁用 TCP 拖拽
     */
    disable() {
        this.config.enabled = false;
        if (this.tcpVisualizer) {
            this.tcpVisualizer.visible = false;
        }
        if (this.isDragging) {
            this.stopDragging();
        }
        console.log('[TCPDragController] TCP 拖拽已禁用');
    }

    /**
     * 清理資源
     */
    dispose() {
        // 移除事件監聽器
        const canvas = this.renderer.domElement;
        canvas.removeEventListener('mousedown', this.onMouseDown);
        canvas.removeEventListener('mousemove', this.onMouseMove);
        canvas.removeEventListener('mouseup', this.onMouseUp);

        // 移除視覺化組件
        if (this.tcpVisualizer) {
            this.scene.remove(this.tcpVisualizer);
            this.tcpVisualizer.geometry.dispose();
            this.tcpVisualizer.material.dispose();
        }

        // 取消 ROS 發布器
        rosPublisherManager.unadvertise(this.config.tcpPoseTopic);
        rosPublisherManager.unadvertise(this.config.jointCommandTopic);

        console.log('[TCPDragController] 資源已清理');
    }
}
