/**
 * IKSolverManager - 逆運動學求解管理器
 * 
 * 功能：
 * - MoveIt! IK 服務調用
 * - 本地 IK 求解（備用）
 * - IK 結果驗證和過濾
 * - 關節限制檢查
 * 
 * @module IKSolverManager
 * <AUTHOR> UI Control Team
 * @version 1.0.0
 */

// ==================== 導入區塊 ====================
import { rosConnectionManager } from '../ros/ros_connection_manager.js';
import { rosStateManager } from '../ros/ros_state_manager.js';

// ==================== 類別定義 ====================

/**
 * IKSolverManager 類別 - 逆運動學求解管理器
 * 
 * @class IKSolverManager
 * @description 管理機器人的逆運動學求解功能
 */
export class IKSolverManager {
    
    // ==================== 建構函數 ====================
    
    /**
     * 建構函數
     * 
     * @param {Object} options - 配置選項
     */
    constructor(options = {}) {
        // 配置參數
        this.config = {
            ikServiceName: '/compute_ik',
            planningGroup: 'manipulator',
            endEffectorLink: 'tool0',
            baseFrame: 'base_link',
            timeout: 5.0,
            attempts: 10,
            enableFallback: true,
            validateSolution: true,
            ...options
        };

        // IK 服務客戶端
        this.ikService = null;
        this.isServiceAvailable = false;

        // 關節限制
        this.jointLimits = new Map();

        // 求解統計
        this.stats = {
            totalRequests: 0,
            successfulSolutions: 0,
            failedSolutions: 0,
            averageSolveTime: 0
        };

        this.initialize();
    }

    // ==================== 初始化方法 ====================

    /**
     * 初始化 IK 求解器
     */
    initialize() {
        this.setupIKService();
        this.loadJointLimits();

        console.log('[IKSolverManager] IK 求解器管理器已初始化');
    }

    /**
     * 設置 IK 服務
     */
    setupIKService() {
        const ros = rosConnectionManager.getROS();
        if (!ros) {
            console.warn('[IKSolverManager] ROS 未連接，無法設置 IK 服務');
            return;
        }

        this.ikService = new window.ROSLIB.Service({
            ros: ros,
            name: this.config.ikServiceName,
            serviceType: 'moveit_msgs/GetPositionIK'
        });

        // 檢查服務可用性
        this.checkServiceAvailability();
    }

    /**
     * 檢查服務可用性
     */
    checkServiceAvailability() {
        // 這裡可以實現服務可用性檢查
        // 由於 roslib.js 沒有直接的服務檢查方法，我們假設服務可用
        this.isServiceAvailable = true;
        console.log('[IKSolverManager] IK 服務已設置');
    }

    /**
     * 載入關節限制
     */
    loadJointLimits() {
        // 這裡可以從 URDF 或配置文件載入關節限制
        // 暫時使用預設值
        const defaultLimits = {
            'shoulder_pan_joint': { min: -Math.PI, max: Math.PI },
            'shoulder_lift_joint': { min: -Math.PI, max: Math.PI },
            'elbow_joint': { min: -Math.PI, max: Math.PI },
            'wrist_1_joint': { min: -Math.PI, max: Math.PI },
            'wrist_2_joint': { min: -Math.PI, max: Math.PI },
            'wrist_3_joint': { min: -Math.PI, max: Math.PI }
        };

        Object.entries(defaultLimits).forEach(([joint, limits]) => {
            this.jointLimits.set(joint, limits);
        });

        console.log('[IKSolverManager] 關節限制已載入');
    }

    // ==================== IK 求解方法 ====================

    /**
     * 求解逆運動學
     * 
     * @param {Object} targetPose - 目標位姿
     * @param {Object} currentJointState - 當前關節狀態
     * @param {Object} options - 求解選項
     * @returns {Promise<Object>} IK 求解結果
     */
    async solveIK(targetPose, currentJointState, options = {}) {
        const startTime = Date.now();
        this.stats.totalRequests++;

        try {
            // 構建 IK 請求
            const ikRequest = this.buildIKRequest(targetPose, currentJointState, options);

            // 調用 IK 服務
            const result = await this.callIKService(ikRequest);

            // 驗證求解結果
            if (this.config.validateSolution && result.solution) {
                const isValid = this.validateSolution(result.solution.joint_state);
                if (!isValid) {
                    throw new Error('IK 求解結果驗證失敗');
                }
            }

            // 更新統計
            this.stats.successfulSolutions++;
            const solveTime = Date.now() - startTime;
            this.updateAverageSolveTime(solveTime);

            console.log(`[IKSolverManager] IK 求解成功，耗時: ${solveTime}ms`);
            return {
                success: true,
                solution: result.solution.joint_state,
                errorCode: result.error_code,
                solveTime: solveTime
            };

        } catch (error) {
            this.stats.failedSolutions++;
            console.error('[IKSolverManager] IK 求解失敗:', error);

            // 嘗試備用求解方法
            if (this.config.enableFallback) {
                return this.fallbackSolve(targetPose, currentJointState);
            }

            return {
                success: false,
                error: error.message,
                solveTime: Date.now() - startTime
            };
        }
    }

    /**
     * 構建 IK 請求
     * 
     * @param {Object} targetPose - 目標位姿
     * @param {Object} currentJointState - 當前關節狀態
     * @param {Object} options - 選項
     * @returns {Object} IK 請求
     */
    buildIKRequest(targetPose, currentJointState, options = {}) {
        const {
            attempts = this.config.attempts,
            timeout = this.config.timeout,
            planningGroup = this.config.planningGroup
        } = options;

        return {
            ik_request: {
                group_name: planningGroup,
                robot_state: {
                    joint_state: {
                        header: {
                            stamp: { sec: Math.floor(Date.now() / 1000), nsec: 0 }
                        },
                        name: currentJointState.name || [],
                        position: currentJointState.position || [],
                        velocity: currentJointState.velocity || [],
                        effort: currentJointState.effort || []
                    }
                },
                constraints: {
                    joint_constraints: [],
                    position_constraints: [],
                    orientation_constraints: [],
                    visibility_constraints: []
                },
                avoid_collisions: true,
                ik_link_name: this.config.endEffectorLink,
                pose_stamped: {
                    header: {
                        stamp: { sec: Math.floor(Date.now() / 1000), nsec: 0 },
                        frame_id: this.config.baseFrame
                    },
                    pose: targetPose
                },
                timeout: { sec: Math.floor(timeout), nsec: (timeout % 1) * 1e9 },
                attempts: attempts
            }
        };
    }

    /**
     * 調用 IK 服務
     * 
     * @param {Object} request - IK 請求
     * @returns {Promise<Object>} 服務回應
     */
    callIKService(request) {
        return new Promise((resolve, reject) => {
            if (!this.ikService || !this.isServiceAvailable) {
                reject(new Error('IK 服務不可用'));
                return;
            }

            this.ikService.callService(request, 
                (result) => {
                    if (result.error_code.val === 1) { // SUCCESS
                        resolve(result);
                    } else {
                        reject(new Error(`IK 求解失敗，錯誤代碼: ${result.error_code.val}`));
                    }
                },
                (error) => {
                    reject(new Error(`IK 服務調用失敗: ${error}`));
                }
            );
        });
    }

    /**
     * 驗證 IK 求解結果
     * 
     * @param {Object} jointState - 關節狀態
     * @returns {boolean} 是否有效
     */
    validateSolution(jointState) {
        if (!jointState.name || !jointState.position) {
            return false;
        }

        // 檢查關節限制
        for (let i = 0; i < jointState.name.length; i++) {
            const jointName = jointState.name[i];
            const position = jointState.position[i];
            const limits = this.jointLimits.get(jointName);

            if (limits) {
                if (position < limits.min || position > limits.max) {
                    console.warn(`[IKSolverManager] 關節 ${jointName} 超出限制: ${position}`);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 備用求解方法
     * 
     * @param {Object} targetPose - 目標位姿
     * @param {Object} currentJointState - 當前關節狀態
     * @returns {Object} 求解結果
     */
    fallbackSolve(targetPose, currentJointState) {
        console.log('[IKSolverManager] 使用備用求解方法');
        
        // 這裡可以實現簡單的數值 IK 求解
        // 暫時返回失敗結果
        return {
            success: false,
            error: '備用求解方法尚未實現',
            solveTime: 0
        };
    }

    // ==================== 工具方法 ====================

    /**
     * 更新平均求解時間
     * 
     * @param {number} newTime - 新的求解時間
     */
    updateAverageSolveTime(newTime) {
        const totalSolutions = this.stats.successfulSolutions;
        this.stats.averageSolveTime = 
            (this.stats.averageSolveTime * (totalSolutions - 1) + newTime) / totalSolutions;
    }

    /**
     * 獲取求解統計
     * 
     * @returns {Object} 統計信息
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 重置統計
     */
    resetStats() {
        this.stats = {
            totalRequests: 0,
            successfulSolutions: 0,
            failedSolutions: 0,
            averageSolveTime: 0
        };
    }

    /**
     * 清理資源
     */
    dispose() {
        if (this.ikService) {
            this.ikService = null;
        }
        this.isServiceAvailable = false;
        console.log('[IKSolverManager] 資源已清理');
    }
}
